import { z } from 'zod';
import { UserRole, UserStatus } from '@aperion/shared';

// Extended user interface for command center operations
export interface AdminUser {
  id: string;
  cognitoUserId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  phoneNumber?: string; // Added for consistency with database field
  role: UserRole;
  status: UserStatus;
  serviceUserId: string;
  userType: string;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  // Additional fields for admin view
  company?: string;
  subscription?: string;
  lastActive?: string;
}

// User creation request
export interface CreateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string | undefined;
  phoneNumber?: string | undefined; // Added for consistency with database field
  role: UserRole;
  status?: UserStatus | undefined; // User status (defaults to 'pending' if not provided)
  userType: string;
  company?: string | undefined;
  subscription?: string | undefined;
  cognitoUserId?: string | undefined; // Optional for backward compatibility, but required for Cognito integration
}

// User update request
export interface UpdateUserRequest {
  firstName?: string | undefined;
  lastName?: string | undefined;
  email?: string | undefined;
  phone?: string | undefined;
  phoneNumber?: string | undefined; // Added for consistency with database field
  role?: UserRole | undefined;
  status?: UserStatus | undefined;
  company?: string | undefined;
  subscription?: string | undefined;
}

// User query parameters
export interface UserQueryParams {
  page?: number | undefined;
  limit?: number | undefined;
  search?: string | undefined;
  role?: UserRole | undefined;
  status?: UserStatus | undefined;
  company?: string | undefined;
  sortBy?: string | undefined;
  sortOrder?: 'asc' | 'desc' | undefined;
}

// User statistics
export interface UserStatistics {
  totalUsers: number;
  activeUsers: number;
  suspendedUsers: number;
  newUsersThisMonth: number;
  usersByRole: Record<UserRole, number>;
  usersByStatus: Record<UserStatus, number>;
}

// Enhanced phone validation for specific country codes
const phoneValidation = z.string().refine((phone) => {
  if (!phone) return true; // Optional field

  // Must start with +1 or +91
  if (!phone.startsWith('+1') && !phone.startsWith('+91')) {
    return false;
  }

  // Extract digits after country code
  const digits = phone.startsWith('+1') ? phone.substring(2) : phone.substring(3);

  // Must be exactly 10 digits
  if (!/^\d{10}$/.test(digits)) {
    return false;
  }

  // US numbers (+1) cannot start with 0 or 1
  if (phone.startsWith('+1') && (digits.startsWith('0') || digits.startsWith('1'))) {
    return false;
  }

  return true;
}, {
  message: 'Phone number must be +1 or +91 followed by exactly 10 digits. US numbers cannot start with 0 or 1.'
});

// Validation schemas
export const createUserSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(100),
  lastName: z.string().min(1, 'Last name is required').max(100),
  email: z.string().email('Invalid email format'),
  phone: phoneValidation.optional(),
  phoneNumber: phoneValidation.optional(), // Added for consistency
  role: z.nativeEnum(UserRole),
  userType: z.string().min(1, 'User type is required'),
  company: z.string().max(200).optional(),
  subscription: z.string().max(100).optional(),
  cognitoUserId: z.string().optional(), // Optional for backward compatibility
});

export const updateUserSchema = z.object({
  firstName: z.string().min(1).max(100).optional(),
  lastName: z.string().min(1).max(100).optional(),
  email: z.string().email().optional(),
  phone: phoneValidation.optional(),
  phoneNumber: phoneValidation.optional(), // Added for consistency
  role: z.nativeEnum(UserRole).optional(),
  status: z.nativeEnum(UserStatus).optional(),
  company: z.string().max(200).optional(),
  subscription: z.string().max(100).optional(),
});

export const userQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  search: z.string().optional(),
  role: z.nativeEnum(UserRole).optional(),
  status: z.nativeEnum(UserStatus).optional(),
  company: z.string().optional(),
  sortBy: z.string().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

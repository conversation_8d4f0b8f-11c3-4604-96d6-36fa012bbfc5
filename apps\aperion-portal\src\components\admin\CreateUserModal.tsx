import React, { useState } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2, User, Mail, Phone, Building2, CreditCard, Shield, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Define UserRole enum locally to avoid import issues
enum UserRole {
  MEMBER = 'member',
  EMPLOYER = 'employer',
  WELLNESS_COACH = 'wellness-coach',
  SYSTEM_ADMIN = 'system-admin',
}

// Define UserStatus enum locally
enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
}

// Simple form data interface
interface CreateUserFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string | undefined;
  phoneCountryCode: string;
  phoneNumber: string;
  role: UserRole;
  status: UserStatus;
  company?: string | undefined;
  subscription?: string | undefined;
  userType: string;
  companyName?: string; // For employer users
}

interface CreateUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateUser: (userData: CreateUserFormData) => Promise<boolean>;
  companies: string[];
}

// Status configurations
const statusConfigs = {
  [UserStatus.ACTIVE]: {
    label: 'Active',
    color: 'bg-green-100 text-green-800',
    description: 'User has full access to the platform',
  },
  [UserStatus.INACTIVE]: {
    label: 'Inactive',
    color: 'bg-gray-100 text-gray-800',
    description: 'User account is temporarily disabled',
  },
  [UserStatus.PENDING]: {
    label: 'Pending',
    color: 'bg-yellow-100 text-yellow-800',
    description: 'User account is awaiting activation',
  },
  [UserStatus.SUSPENDED]: {
    label: 'Suspended',
    color: 'bg-red-100 text-red-800',
    description: 'User account is suspended due to policy violation',
  },
};

// Role configurations
const roleConfigs = {
  [UserRole.MEMBER]: {
    label: 'Member',
    description: 'Individual employee with health benefits access',
    color: 'bg-purple-100 text-purple-800',
    requiredFields: ['firstName', 'lastName', 'email'],
    optionalFields: ['phone', 'company', 'subscription'],
    defaultCompany: 'TechCorp Solutions',
    defaultSubscription: 'Premium Health Plan',
  },
  [UserRole.EMPLOYER]: {
    label: 'Employer',
    description: 'Company administrator with employee management access',
    color: 'bg-blue-100 text-blue-800',
    requiredFields: ['companyName', 'email', 'company'],
    optionalFields: ['phone', 'subscription'],
    defaultCompany: 'Global Health Corp',
    defaultSubscription: 'Enterprise',
  },
  [UserRole.WELLNESS_COACH]: {
    label: 'Wellness Coach',
    description: 'Health coaching professional with member access',
    color: 'bg-green-100 text-green-800',
    requiredFields: ['firstName', 'lastName', 'email'],
    optionalFields: ['phone', 'company'],
    defaultCompany: 'WellnessTech Inc',
    defaultSubscription: undefined,
  },
  [UserRole.SYSTEM_ADMIN]: {
    label: 'System Admin',
    description: 'Platform administrator with full system access',
    color: 'bg-red-100 text-red-800',
    requiredFields: ['firstName', 'lastName', 'email'],
    optionalFields: ['phone'],
    defaultCompany: 'Platform Administration',
    defaultSubscription: undefined,
  },
};

// Phone validation functions
const validatePhoneNumber = (countryCode: string, phoneNumber: string): { isValid: boolean; error?: string } => {
  if (!phoneNumber.trim()) {
    return { isValid: false, error: 'Phone number is required' };
  }

  // Remove any non-digit characters for validation
  const cleanPhone = phoneNumber.replace(/\D/g, '');

  // Validate exactly 10 digits for both +1 and +91
  if (cleanPhone.length !== 10) {
    return { isValid: false, error: 'Phone number must be exactly 10 digits' };
  }

  // Additional validation for US numbers (+1) - cannot start with 0 or 1
  if (countryCode === '+1' && (cleanPhone.startsWith('0') || cleanPhone.startsWith('1'))) {
    return { isValid: false, error: 'US phone numbers cannot start with 0 or 1' };
  }

  return { isValid: true };
};

export function CreateUserModal({ open, onOpenChange, onCreateUser, companies }: CreateUserModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [phoneError, setPhoneError] = useState<string>('');
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    phoneCountryCode: '+1',
    phoneNumber: '',
    role: UserRole.MEMBER,
    status: UserStatus.PENDING, // Default to pending for new users
    company: '',
    subscription: '',
    companyName: '', // For employer users
  });

  console.log('CreateUserModal render - open:', open);

  const roleConfig = roleConfigs[formData.role];

  // Update default values when role changes
  React.useEffect(() => {
    if (roleConfig) {
      setFormData(prev => ({
        ...prev,
        company: roleConfig.defaultCompany || '',
        subscription: roleConfig.defaultSubscription || '',
      }));
    }
  }, [formData.role, roleConfig]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate phone number
      const phoneValidation = validatePhoneNumber(formData.phoneCountryCode, formData.phoneNumber);
      if (!phoneValidation.isValid) {
        setPhoneError(phoneValidation.error || 'Invalid phone number');
        setIsSubmitting(false);
        return;
      }

      // Clear phone error if validation passes
      setPhoneError('');

      // Combine country code and phone number
      const fullPhoneNumber = formData.phoneNumber.trim()
        ? `${formData.phoneCountryCode}${formData.phoneNumber.replace(/\D/g, '')}`
        : '';

      // Map role to userType for backend
      const userTypeMapping = {
        [UserRole.MEMBER]: 'member',
        [UserRole.EMPLOYER]: 'employer',
        [UserRole.WELLNESS_COACH]: 'wellness_coach',
        [UserRole.SYSTEM_ADMIN]: 'system_admin',
      };

      // For employer users, store company name in firstName and set lastName to null
      const userData = formData.role === UserRole.EMPLOYER ? {
        ...formData,
        firstName: formData.companyName,
        lastName: "null",
        userType: userTypeMapping[formData.role],
        phone: fullPhoneNumber || undefined,
        company: formData.company || undefined,
        subscription: formData.subscription || undefined,
      } : {
        ...formData,
        userType: userTypeMapping[formData.role],
        phone: fullPhoneNumber || undefined,
        company: formData.company || undefined,
        subscription: formData.subscription || undefined,
      };

      const success = await onCreateUser(userData);
      if (success) {
        // Reset form
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          phoneCountryCode: '+1',
          phoneNumber: '',
          role: UserRole.MEMBER,
          status: UserStatus.PENDING, // Default to pending for new users
          company: '',
          subscription: '',
          companyName: '',
        });
        setPhoneError('');
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Error creating user:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    // Reset form
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      phoneCountryCode: '+1',
      phoneNumber: '',
      role: UserRole.MEMBER,
      status: UserStatus.PENDING, // Default to pending for new users
      company: '',
      subscription: '',
      companyName: '',
    });
    setPhoneError('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="w-5 h-5" />
            <span>Create New User</span>
          </DialogTitle>
          <DialogDescription>
            Add a new user to the platform. Select a role to customize the form fields.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Role Selection */}
          <div className="space-y-2">
            <Label htmlFor="role">User Role *</Label>
            <Select
              value={formData.role}
              onValueChange={(value) => setFormData(prev => ({ ...prev, role: value as UserRole }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(roleConfigs).map(([role, config]) => (
                  <SelectItem key={role} value={role}>
                    <div className="flex items-center space-x-2">
                      <Badge className={`text-xs ${config.color}`}>
                        {config.label}
                      </Badge>
                      <span className="text-sm text-slate-600">{config.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Role Description */}
          {roleConfig && (
            <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-md">
              <div className="flex items-center space-x-2">
                <Badge className={`text-xs ${roleConfig.color}`}>
                  {roleConfig.label}
                </Badge>
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  {roleConfig.description}
                </span>
              </div>
            </div>
          )}

          {/* User Status */}
          <div className="space-y-2">
            <Label htmlFor="status" className="flex items-center space-x-1">
              <Shield className="w-4 h-4" />
              <span>User Status *</span>
            </Label>
            <Select
              value={formData.status}
              onValueChange={(value) => setFormData(prev => ({ ...prev, status: value as UserStatus }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(statusConfigs).map(([status, config]) => (
                  <SelectItem key={status} value={status}>
                    <div className="flex items-center space-x-2">
                      <Badge className={`text-xs ${config.color}`}>
                        {config.label}
                      </Badge>
                      <span className="text-sm text-slate-600">{config.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Personal Information - Conditional based on role */}
          {formData.role === UserRole.EMPLOYER ? (
            // Company Name field for employers
            <div className="space-y-2">
              <Label htmlFor="companyName" className="flex items-center space-x-1">
                <Building2 className="w-4 h-4" />
                <span>Company Name *</span>
              </Label>
              <Input
                id="companyName"
                placeholder="Acme Corporation"
                value={formData.companyName}
                onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                required
              />
            </div>
          ) : (
            // First Name and Last Name fields for other roles
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  placeholder="John"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  placeholder="Doe"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  required
                />
              </div>
            </div>
          )}

          {/* Contact Information */}
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center space-x-1">
              <Mail className="w-4 h-4" />
              <span>Email Address *</span>
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone" className="flex items-center space-x-1">
              <Phone className="w-4 h-4" />
              <span>Phone Number *</span>
            </Label>
            <div className="grid grid-cols-3 gap-2">
              {/* Country Code Dropdown */}
              <div className="col-span-1">
                <Select
                  value={formData.phoneCountryCode}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, phoneCountryCode: value }))}
                >
                  <SelectTrigger className={`h-12 text-base transition-all duration-300 border focus:outline-none ${
                    phoneError
                      ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                      : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                  }`}>
                    <SelectValue placeholder="+1" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="+1">🇺🇸 +1</SelectItem>
                    <SelectItem value="+91">🇮🇳 +91</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Phone Number Input */}
              <div className="col-span-2 relative">
                <Input
                  id="phone"
                  type="tel"
                  placeholder="(*************"
                  value={formData.phoneNumber}
                  onChange={(e) => {
                    const value = e.target.value;
                    // Allow only digits, spaces, dashes, and parentheses
                    const cleanValue = value.replace(/[^\d\s\-()]/g, '');
                    setFormData(prev => ({ ...prev, phoneNumber: cleanValue }));
                    // Clear error when user starts typing
                    if (phoneError) setPhoneError('');
                  }}
                  className={`h-12 text-base pr-12 transition-all duration-300 border focus:outline-none ${
                    phoneError
                      ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                      : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                  }`}
                  required
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Phone className="w-4 h-4 text-blue-500" />
                </div>
              </div>
            </div>
            {phoneError && (
              <p className="text-sm text-red-600 flex items-center space-x-1">
                <AlertCircle className="w-4 h-4" />
                <span>{phoneError}</span>
              </p>
            )}
          </div>

          {/* Company Information */}
          {roleConfig?.optionalFields.includes('company') && (
            <div className="space-y-2">
              <Label htmlFor="company" className="flex items-center space-x-1">
                <Building2 className="w-4 h-4" />
                <span>Company {roleConfig.requiredFields.includes('company') ? '*' : ''}</span>
              </Label>
              <Select
                value={formData.company}
                onValueChange={(value) => setFormData(prev => ({ ...prev, company: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select company" />
                </SelectTrigger>
                <SelectContent>
                  {companies.map((company) => (
                    <SelectItem key={company} value={company}>
                      {company}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Subscription Information */}
          {roleConfig?.optionalFields.includes('subscription') && (
            <div className="space-y-2">
              <Label htmlFor="subscription" className="flex items-center space-x-1">
                <CreditCard className="w-4 h-4" />
                <span>Subscription Plan</span>
              </Label>
              <Select
                value={formData.subscription}
                onValueChange={(value) => setFormData(prev => ({ ...prev, subscription: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select subscription" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Basic Health Plan">Basic Health Plan</SelectItem>
                  <SelectItem value="Premium Health Plan">Premium Health Plan</SelectItem>
                  <SelectItem value="Enterprise">Enterprise</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Create User
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
